package com.inboyu.sales.domain.reserve.model;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 预约看房实体
 */
@Getter
@Builder
public class ReserveSeeHouse {

    /**
     * 自增ID，无业务含义
     */
    private Long id;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 记录版本号（乐观锁）
     */
    private Long version;

    /**
     * 是否删除，0否，1是
     */
    private Integer deleted;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 员工ID
     */
    private Long staffId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 户型ID
     */
    private Long roomTypeId;

    /**
     * 房间ID
     */
    private Long roomId;

    /**
     * 预约日期
     */
    private LocalDate reserveDate;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

}

package com.inboyu.sales.domain.store.service;

import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.domain.store.model.StoreSalesConfig;

public interface StoreSalesConfigDomainService {

    /**
     * 创建门店销售配置
     *
     * @param storeSalesConfig 门店销售配置
     * @return 创建后的配置
     */
    StoreSalesConfig createStoreSalesConfig(StoreSalesConfig storeSalesConfig);

    /**
     * 根据门店ID查询配置
     *
     * @param storeId 门店ID
     * @return 门店销售配置
     */
    StoreSalesConfig getStoreSalesConfigByStoreId(StoreId storeId);

    /**
     * 更新门店销售配置
     *
     * @param storeSalesConfig 门店销售配置
     * @return 更新后的配置
     */
    StoreSalesConfig updateStoreSalesConfig(StoreSalesConfig storeSalesConfig);
}

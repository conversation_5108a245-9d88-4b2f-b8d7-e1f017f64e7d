package com.inboyu.sales.domain.store.model;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2025/9/3
 */
@Getter
@Builder
public class StoreSalesConfig {

    /**
     * 自增ID，无业务含义
     */
    private Long id;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 记录版本号（乐观锁）
     */
    private Long version;

    /**
     * 是否删除，0否，1是
     */
    private Integer deleted;

    /**
     * 门店ID
     */
    private final StoreId storeId;

    /**
     * 是否开放预约看房，字典维护
     */
    private SeeHouseEnabled seeHouseEnabled;

    /**
     * 预约看房开放日期
     */
    private LocalDate seeHouseDate;

    /**
     * 预约看房可选星期，字典维护
     */
    private SeeHouseWeekDay seeHouseWeekDay;

    /**
     * 预约起始时间
     */
    private LocalTime seeHouseTimeStart;

    /**
     * 预约截止时间
     */
    private LocalTime seeHouseTimeEnd;

    /**
     * 是否开放预约具体时间，字典维护
     */
    private TimeEnabled timeEnabled;

    /**
     * 是否开放当天预约，字典维护
     */
    private SeeHouseTodayEnabled seeHouseTodayEnabled;

    /**
     * 可预约最大时间范围，字典维护
     */
    private SeeHouseValidDay seeHouseValidDay;

//    /**
//     * 业务方法：检查是否允许预约看房
//     */
//    public boolean isAllowSeeHouse() {
//        return seeHouseEnabled != null && seeHouseEnabled;
//    }
//
//    /**
//     * 业务方法：检查是否允许预约具体时间
//     */
//    public boolean isAllowTimeSelection() {
//        return timeEnabled != null && timeEnabled;
//    }
//
//    /**
//     * 业务方法：检查是否允许当天预约
//     */
//    public boolean isAllowTodayReservation() {
//        return seeHouseTodayEnabled != null && seeHouseTodayEnabled;
//    }
//
//    /**
//     * 业务方法：获取有效预约天数
//     */
//    public int getValidReservationDays() {
//        return seeHouseValidDay != null ? seeHouseValidDay : 7;
//    }
}

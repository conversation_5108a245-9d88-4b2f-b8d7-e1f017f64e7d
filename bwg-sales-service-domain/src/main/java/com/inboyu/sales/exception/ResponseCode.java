package com.inboyu.sales.exception;

import com.inboyu.spring.cloud.starter.common.constant.ResponseCodeInterface;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年07月29日 20:04
 */
public enum ResponseCode implements ResponseCodeInterface {

    /** 销售服务错误码范围 16000 ～ 16999*/
    SUCCESS(0, "成功");
    /** 销售服务错误码范围 16000 ～ 16999*/
    private final int code;
    private final String msg;

    ResponseCode(int code, String des) {
        this.code = code;
        this.msg = des;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}

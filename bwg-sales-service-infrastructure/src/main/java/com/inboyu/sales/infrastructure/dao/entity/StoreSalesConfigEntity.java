package com.inboyu.sales.infrastructure.dao.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;
import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;

import java.time.LocalDate;
import java.time.LocalTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_store_sales_config")
@Comment("门店销售配置表")
public class StoreSalesConfigEntity extends BaseEntity {

    @Column(name = "id")
    @Comment("自增ID，无业务含义")
    private Long id;

    @Column(name = "store_id", nullable = false)
    @Comment("门店ID")
    private Long storeId;

    @Column(name = "see_house_enabled", nullable = false, length = 50)
    @Comment("是否开放预约看房，字典维护")
    private String seeHouseEnabled;

    @Column(name = "see_house_date", nullable = false)
    @Comment("预约看房开放日期")
    private LocalDate seeHouseDate;

    @Column(name = "see_house_week_day", nullable = false, length = 50)
    @Comment("预约看房可选星期，字典维护")
    private String seeHouseWeekDay;

    @Column(name = "see_house_time_start", nullable = false)
    @Comment("预约起始时间")
    private LocalTime seeHouseTimeStart;

    @Column(name = "see_house_time_end", nullable = false)
    @Comment("预约截止时间")
    private LocalTime seeHouseTimeEnd;

    @Column(name = "time_enabled", nullable = false, length = 50)
    @Comment("是否开放预约具体时间，字典维护")
    private String timeEnabled;

    @Column(name = "see_house_today_enabled", nullable = false, length = 50)
    @Comment("是否开放当天预约，字典维护")
    private String seeHouseTodayEnabled;

    @Column(name = "see_house_valid_day", nullable = false, length = 50)
    @Comment("可预约最大时间范围，字典维护")
    private Integer seeHouseValidDay;

}

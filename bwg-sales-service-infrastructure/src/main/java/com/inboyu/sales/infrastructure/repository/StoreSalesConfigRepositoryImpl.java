package com.inboyu.sales.infrastructure.repository;

import com.inboyu.constant.DeleteFlag;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.domain.store.model.StoreSalesConfig;
import com.inboyu.sales.domain.store.repository.StoreSalesConfigRepository;
import com.inboyu.sales.infrastructure.builder.StoreSalesConfigBuilder;
import com.inboyu.sales.infrastructure.dao.StoreSalesConfigDao;
import com.inboyu.sales.infrastructure.dao.entity.StoreSalesConfigEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

@Repository
public class StoreSalesConfigRepositoryImpl implements StoreSalesConfigRepository {

    @Autowired
    private StoreSalesConfigDao storeSalesConfigDao;

    @Autowired
    private StoreSalesConfigBuilder storeSalesConfigBuilder;

    @Override
    public StoreSalesConfig save(StoreSalesConfig storeSalesConfig) {
        StoreSalesConfigEntity entity = storeSalesConfigBuilder.toStoreSalesConfigEntity(storeSalesConfig);
        if (entity.getId() == null) {
            // 新增时设置创建时间
            entity.setCreateTime(LocalDateTime.now());
            entity.setDeleted(0);
            entity.setVersion(0L);
        }
        entity.setModifyTime(LocalDateTime.now());
        
        StoreSalesConfigEntity savedEntity = storeSalesConfigDao.save(entity);
        return storeSalesConfigBuilder.toStoreSalesConfig(savedEntity);
    }



    @Override
    public StoreSalesConfig findByStoreId(StoreId storeId) {
        StoreSalesConfigEntity entity = storeSalesConfigDao.findByStoreIdAndDeleted(storeId.getValue(), DeleteFlag.NOT_DELETED);
        return storeSalesConfigBuilder.toStoreSalesConfig(entity);
    }

    @Override
    public StoreSalesConfig update(StoreSalesConfig storeSalesConfig) {
        return save(storeSalesConfig);
    }

    @Override
    public boolean existsByStoreId(StoreId storeId) {
        return storeSalesConfigDao.existsByStoreIdAndDeleted(storeId.getValue(), DeleteFlag.NOT_DELETED);
    }
}

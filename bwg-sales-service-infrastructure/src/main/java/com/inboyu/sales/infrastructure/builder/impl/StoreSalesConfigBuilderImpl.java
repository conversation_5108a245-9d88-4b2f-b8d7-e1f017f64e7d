package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.sales.domain.store.model.*;
import com.inboyu.sales.infrastructure.builder.StoreSalesConfigBuilder;
import com.inboyu.sales.infrastructure.dao.entity.StoreSalesConfigEntity;
import org.springframework.stereotype.Component;

@Component
public class StoreSalesConfigBuilderImpl implements StoreSalesConfigBuilder {

    @Override
    public StoreSalesConfigEntity toStoreSalesConfigEntity(StoreSalesConfig storeSalesConfig) {
        if (storeSalesConfig == null) {
            return null;
        }

        StoreSalesConfigEntity entity = new StoreSalesConfigEntity();
        entity.setId(storeSalesConfig.getId());
        entity.setCreateTime(storeSalesConfig.getCreateTime());
        entity.setModifyTime(storeSalesConfig.getModifyTime());
        entity.setVersion(storeSalesConfig.getVersion());
        entity.setDeleted(storeSalesConfig.getDeleted());
        entity.setStoreId(storeSalesConfig.getStoreId().getValue());
        entity.setSeeHouseEnabled(storeSalesConfig.getSeeHouseEnabled().getCode());
        entity.setSeeHouseDate(storeSalesConfig.getSeeHouseDate());
        entity.setSeeHouseWeekDay(storeSalesConfig.getSeeHouseWeekDay().getCode());
        entity.setSeeHouseTimeStart(storeSalesConfig.getSeeHouseTimeStart());
        entity.setSeeHouseTimeEnd(storeSalesConfig.getSeeHouseTimeEnd());
        entity.setTimeEnabled(storeSalesConfig.getTimeEnabled().getCode());
        entity.setSeeHouseTodayEnabled(storeSalesConfig.getSeeHouseTodayEnabled().getCode());
        entity.setSeeHouseValidDay(extractValidDayValue(storeSalesConfig.getSeeHouseValidDay()));

        return entity;
    }

    @Override
    public StoreSalesConfig toStoreSalesConfig(StoreSalesConfigEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return StoreSalesConfig.builder()
                .id(entity.getId())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .version(entity.getVersion())
                .deleted(entity.getDeleted())
                .storeId(StoreId.valueOf(entity.getStoreId()))
                .seeHouseEnabled(SeeHouseEnabled.builder()
                        .code(entity.getSeeHouseEnabled())
                        .value(entity.getSeeHouseEnabled())
                        .build())
                .seeHouseDate(entity.getSeeHouseDate())
                .seeHouseWeekDay(SeeHouseWeekDay.builder()
                        .code(entity.getSeeHouseWeekDay())
                        .value(entity.getSeeHouseWeekDay())
                        .build())
                .seeHouseTimeStart(entity.getSeeHouseTimeStart())
                .seeHouseTimeEnd(entity.getSeeHouseTimeEnd())
                .timeEnabled(TimeEnabled.builder()
                        .code(entity.getTimeEnabled())
                        .value(entity.getTimeEnabled())
                        .build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder()
                        .code(entity.getSeeHouseTodayEnabled())
                        .value(entity.getSeeHouseTodayEnabled())
                        .build())
                .seeHouseValidDay(buildSeeHouseValidDay(entity.getSeeHouseValidDay()))
                .build();
    }

    /**
     * 从SeeHouseValidDay值对象中提取Integer值
     */
    private Integer extractValidDayValue(SeeHouseValidDay seeHouseValidDay) {
        if (seeHouseValidDay == null || seeHouseValidDay.getCode() == null) {
            return null;
        }

        String code = seeHouseValidDay.getCode();
        // 从编码中提取数字部分
        if (code.contains(".")) {
            String[] parts = code.split("\\.");
            if (parts.length > 1) {
                try {
                    return Integer.parseInt(parts[parts.length - 1]);
                } catch (NumberFormatException e) {
                    return null;
                }
            }
        }
        return null;
    }

    /**
     * 根据Integer值构建SeeHouseValidDay值对象
     */
    private SeeHouseValidDay buildSeeHouseValidDay(Integer validDay) {
        if (validDay == null) {
            return null;
        }

        String code;
        switch (validDay) {
            case 7:
                code = SeeHouseValidDay.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7;
                break;
            case 14:
                code = SeeHouseValidDay.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_14;
                break;
            case 30:
                code = SeeHouseValidDay.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_30;
                break;
            default:
                code = "store_sales_config_see_house_valid_day." + validDay;
                break;
        }

        return SeeHouseValidDay.builder()
                .code(code)
                .value(validDay.toString())
                .build();
    }
}

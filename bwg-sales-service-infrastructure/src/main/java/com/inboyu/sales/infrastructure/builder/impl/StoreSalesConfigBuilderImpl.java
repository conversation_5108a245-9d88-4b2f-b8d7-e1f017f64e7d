package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.sales.domain.store.model.*;
import com.inboyu.sales.infrastructure.builder.StoreSalesConfigBuilder;
import com.inboyu.sales.infrastructure.dao.entity.StoreSalesConfigEntity;
import org.springframework.stereotype.Component;

@Component
public class StoreSalesConfigBuilderImpl implements StoreSalesConfigBuilder {

    @Override
    public StoreSalesConfigEntity toStoreSalesConfigEntity(StoreSalesConfig storeSalesConfig) {
        if (storeSalesConfig == null) {
            return null;
        }

        StoreSalesConfigEntity entity = new StoreSalesConfigEntity();
        entity.setId(storeSalesConfig.getId());
        entity.setCreateTime(storeSalesConfig.getCreateTime());
        entity.setModifyTime(storeSalesConfig.getModifyTime());
        entity.setVersion(storeSalesConfig.getVersion());
        entity.setDeleted(storeSalesConfig.getDeleted());
        entity.setStoreId(storeSalesConfig.getStoreId().getValue());
        entity.setSeeHouseEnabled(storeSalesConfig.getSeeHouseEnabled().getCode());
        entity.setSeeHouseDate(storeSalesConfig.getSeeHouseDate());
        entity.setSeeHouseWeekDay(storeSalesConfig.getSeeHouseWeekDay().getCode());
        entity.setSeeHouseTimeStart(storeSalesConfig.getSeeHouseTimeStart());
        entity.setSeeHouseTimeEnd(storeSalesConfig.getSeeHouseTimeEnd());
        entity.setTimeEnabled(storeSalesConfig.getTimeEnabled().getCode());
        entity.setSeeHouseTodayEnabled(storeSalesConfig.getSeeHouseTodayEnabled().getCode());
        entity.setSeeHouseValidDay(storeSalesConfig.getSeeHouseValidDay());

        return entity;
    }

    @Override
    public StoreSalesConfig toStoreSalesConfig(StoreSalesConfigEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return StoreSalesConfig.builder()
                .id(entity.getId())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .version(entity.getVersion())
                .deleted(entity.getDeleted())
                .storeId(StoreId.valueOf(entity.getStoreId()))
                .seeHouseEnabled(SeeHouseEnabled.builder().build())
                .seeHouseDate(entity.getSeeHouseDate())
                .seeHouseWeekDay(SeeHouseWeekDay.builder().build())
                .seeHouseTimeStart(entity.getSeeHouseTimeStart())
                .seeHouseTimeEnd(entity.getSeeHouseTimeEnd())
                .timeEnabled(TimeEnabled.builder().build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder().build())
                .seeHouseValidDay(entity.getSeeHouseValidDay())
                .build();
    }
}

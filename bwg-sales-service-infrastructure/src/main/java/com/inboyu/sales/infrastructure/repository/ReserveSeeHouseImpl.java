package com.inboyu.sales.infrastructure.repository;

import com.inboyu.sales.infrastructure.dao.ReserveSeeHouseDao;
import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class ReserveSeeHouseImpl {

    private final ReserveSeeHouseDao reserveSeeHouseDao;

    /**
     * 分页查询预约看房
     *
     * @param pageNum    当前页
     * @param pageSize   每页条数
     * @param customerId 客户ID（可选）
     * @param storeId    门店ID（可选）
     * @return {@link Pagination}<{@link ReserveSeeHouseEntity}>
     */
    public Pagination<ReserveSeeHouseEntity> pageReserveSeeHouse(Integer pageNum, Integer pageSize, Long customerId, Long storeId) {
        // 创建分页参数
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);

        // 查询数据
        Page<ReserveSeeHouseEntity> entityPage = reserveSeeHouseDao.pageByCustomerIdAndStoreId(customerId, storeId, pageable);

        // 获取查询结果
        List<ReserveSeeHouseEntity> reserveList = entityPage.getContent();

        // 构造分页结果
        return new Pagination<>(
                entityPage.getNumber() + 1,
                entityPage.getSize(),
                entityPage.getTotalPages(),
                entityPage.getTotalElements(),
                reserveList);
    }
}
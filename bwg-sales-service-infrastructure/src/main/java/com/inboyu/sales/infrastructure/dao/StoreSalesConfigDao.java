package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.infrastructure.dao.entity.StoreSalesConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;


import java.util.Optional;

public interface StoreSalesConfigDao extends JpaRepository<StoreSalesConfigEntity, Long> {

    /**
     * 根据门店ID查询配置
     *
     * @param storeId 门店ID
     * @return 门店销售配置
     */
    StoreSalesConfigEntity findByStoreIdAndDeleted(Long storeId, Integer deleted);

    /**
     * 检查门店配置是否存在
     *
     * @param storeId 门店ID
     * @return 是否存在
     */
    boolean existsByStoreIdAndDeleted(Long storeId, Integer deleted);
}

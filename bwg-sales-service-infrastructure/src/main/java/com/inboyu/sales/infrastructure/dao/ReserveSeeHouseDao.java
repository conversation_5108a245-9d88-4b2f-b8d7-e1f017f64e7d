package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ReserveSeeHouseDao extends JpaRepository<ReserveSeeHouseEntity, Long> {

    /**
     * 分页查询预约看房
     *
     * @param storeId    门店ID
     * @param customerId 客户ID
     * @param pageable   分页参数
     * @return {@link Page }<{@link ReserveSeeHouseEntity }>
     */
    @Query("SELECT r FROM ReserveSeeHouseEntity r WHERE r.deleted = 0 AND (:storeId IS NULL OR r.storeId = :storeId) AND (:customerId IS NULL OR r.customerId = :customerId) ORDER BY r.createTime DESC")
    Page<ReserveSeeHouseEntity> pageByCustomerIdAndStoreId(Long customerId, Long storeId, Pageable pageable);


    /**
     *
     * @param id
     * @param deleted
     * @return
     */
    ReserveSeeHouseEntity findByIdAndDeleted(Long id, int deleted);


    /**
     * 更新预约看房信息
     *
     * @param id
     * @param reserveSeeHouseEntity
     * <AUTHOR> Copilot
     * @date 2025/01/25
     */
    @Modifying
    @Query("UPDATE ReserveSeeHouseEntity r SET r.customerId = :#{#reserveSeeHouseEntity.customerId}, " +
            "r.staffId = :#{#reserveSeeHouseEntity.staffId}, r.storeId = :#{#reserveSeeHouseEntity.storeId}, " +
            "r.roomTypeId = :#{#reserveSeeHouseEntity.roomTypeId}, r.roomId = :#{#reserveSeeHouseEntity.roomId}, " +
            "r.reserveDate = :#{#reserveSeeHouseEntity.reserveDate}, r.startTime = :#{#reserveSeeHouseEntity.startTime}, " +
            "r.endTime = :#{#reserveSeeHouseEntity.endTime}, r.version = r.version + 1 " +
            "WHERE r.id = :id AND r.deleted = 0")
    void updateReserveSeeHouse(Long id, ReserveSeeHouseEntity reserveSeeHouseEntity);
}
package com.inboyu.sales.app.dto.response.store;

import com.inboyu.sales.app.dto.common.store.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 门店销售配置响应DTO
 */
@Data
@Builder
@Schema(description = "门店销售配置信息")
public class StoreSalesConfigDTO {

    @Schema(description = "自增ID，无业务含义")
    private Long id;

    @Schema(description = "门店ID")
    private Long storeId;

    @Schema(description = "是否开放预约看房")
    private SeeHouseEnabledDTO seeHouseEnabled;

    @Schema(description = "预约看房开放日期")
    private LocalDate seeHouseDate;

    @Schema(description = "预约看房可选星期")
    private SeeHouseWeekDayDTO seeHouseWeekDay;

    @Schema(description = "预约起始时间")
    private LocalTime seeHouseTimeStart;

    @Schema(description = "预约截止时间")
    private LocalTime seeHouseTimeEnd;

    @Schema(description = "是否开放预约具体时间")
    private TimeEnabledDTO timeEnabled;

    @Schema(description = "是否开放当天预约")
    private SeeHouseTodayEnabledDTO seeHouseTodayEnabled;

    @Schema(description = "可预约最大时间范围")
    private SeeHouseValidDayDTO seeHouseValidDay;

    @Schema(description = "记录创建时间")
    private LocalDateTime createTime;

    @Schema(description = "记录修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "记录版本号（乐观锁）")
    private Long version;

    @Schema(description = "是否删除，0否，1是")
    private Integer deleted;
}

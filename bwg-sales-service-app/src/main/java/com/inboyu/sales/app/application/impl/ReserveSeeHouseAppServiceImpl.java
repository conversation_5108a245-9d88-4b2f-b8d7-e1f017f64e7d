package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.application.ReserveSeeHouseAppService;
import com.inboyu.sales.app.dto.converter.ReserveSeeHouseAppConverter;
import com.inboyu.sales.app.dto.request.reserve.ReserveSeeHouseCreateRequestDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseCreateResponseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDetailResponseDTO;
import com.inboyu.sales.domain.reserve.model.ReserveSeeHouse;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.sales.domain.reserve.repository.ReserveSeeHouseRepository;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Log4j2
public class ReserveSeeHouseAppServiceImpl implements ReserveSeeHouseAppService {

    @Autowired
    private ReserveSeeHouseRepository reserveSeeHouseRepository;


    @Autowired
    private ReserveSeeHouseAppConverter reserveSeeHouseAppConverter;


    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public Pagination<ReserveSeeHouseDTO> pageReserveSeeHouse(Integer pageNum, Integer pageSize, Long customerId, Long storeId) {
        Pagination<ReserveSeeHouse> reserveSeeHouses = reserveSeeHouseRepository.pageReserveSeeHouse(pageNum, pageSize, customerId, storeId);
        List<ReserveSeeHouseDTO> storeDTOs = reserveSeeHouses.getList().stream()
                .map(reserveSeeHouseAppConverter::toDTO).toList();
        // 4. 构造分页结果
        return new Pagination<>(
                reserveSeeHouses.getPageNum(),
                reserveSeeHouses.getPageSize(),
                reserveSeeHouses.getTotalPages(),
                reserveSeeHouses.getTotal(),
                storeDTOs);
    }

    @Override
    public ReserveSeeHouseCreateResponseDTO createReserveSeeHouse(ReserveSeeHouseCreateRequestDTO dto) {
        log.info("创建预约看房记录: {}", dto);

        // 2. 将请求 DTO 转换为领域模型
        ReserveSeeHouse reserveSeeHouse = reserveSeeHouseAppConverter.toDomainFromCreateRequest(dto);

        log.info("转换后的预约看房领域模型: {}", reserveSeeHouse);

        // 3. 保存到仓储
        ReserveSeeHouse saved = reserveSeeHouseRepository.save(reserveSeeHouse);

        // 4. 转换为创建响应 DTO 返回
        return reserveSeeHouseAppConverter.toCreateResponseDTO(saved);

    }

    @Override
    public ReserveSeeHouseDetailResponseDTO getReserveSeeHouseDetail(Long id) {
        log.info("获取预约看房详情，ID: {}", id);

        // 1. 根据 ID 查询
        ReserveSeeHouse reserveSeeHouse = reserveSeeHouseRepository.findById(id);

        if (reserveSeeHouse == null) {
            log.warn("预约看房记录不存在，ID: {}", id);
            return null;
        }

        // 2. 转换为详情响应 DTO 返回
        return ReserveSeeHouseDetailResponseDTO.builder()
                .id(reserveSeeHouse.getId())
                .reserveSeeHouseId(reserveSeeHouse.getReserveSeeHouseId())
                .customerId(reserveSeeHouse.getCustomerId())
                .staffId(reserveSeeHouse.getStaffId())
                .storeId(reserveSeeHouse.getStoreId())
                .roomTypeId(reserveSeeHouse.getRoomTypeId())
                .roomId(reserveSeeHouse.getRoomId())
                .reserveDate(reserveSeeHouse.getReserveDate())
                .startTime(reserveSeeHouse.getStartTime())
                .endTime(reserveSeeHouse.getEndTime())
                .build();
    }
}
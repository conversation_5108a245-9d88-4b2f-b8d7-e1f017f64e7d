package com.inboyu.sales.app.dto.converter;

import com.inboyu.sales.app.dto.request.reserve.ReserveSeeHouseCreateRequestDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseCreateResponseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDTO;
import com.inboyu.sales.domain.reserve.model.ReserveSeeHouse;
import org.springframework.stereotype.Component;

@Component
public class ReserveSeeHouseAppConverter {
    public ReserveSeeHouseDTO toDTO(ReserveSeeHouse reserveSeeHouse) {
        return ReserveSeeHouseDTO.builder()
                .id(reserveSeeHouse.getId())
                .reserveSeeHouseId(reserveSeeHouse.getReserveSeeHouseId())
                .createTime(reserveSeeHouse.getCreateTime())
                .modifyTime(reserveSeeHouse.getModifyTime())
                .version(reserveSeeHouse.getVersion())
                .deleted(reserveSeeHouse.getDeleted())
                .customerId(reserveSeeHouse.getCustomerId())
                .staffId(reserveSeeHouse.getStaffId())
                .storeId(reserveSeeHouse.getStoreId())
                .roomTypeId(reserveSeeHouse.getRoomTypeId())
                .roomId(reserveSeeHouse.getRoomId())
                .reserveDate(reserveSeeHouse.getReserveDate())
                .startTime(reserveSeeHouse.getStartTime())
                .endTime(reserveSeeHouse.getEndTime())
                .seeState(reserveSeeHouse.getSeeState())
                .build();
    }

    public ReserveSeeHouse toDomain(ReserveSeeHouseDTO dto) {
        return ReserveSeeHouse.builder()
                .id(dto.getId())
                .reserveSeeHouseId(dto.getReserveSeeHouseId())
                .createTime(dto.getCreateTime())
                .modifyTime(dto.getModifyTime())
                .version(dto.getVersion())
                .deleted(dto.getDeleted())
                .customerId(dto.getCustomerId())
                .staffId(dto.getStaffId())
                .storeId(dto.getStoreId())
                .roomTypeId(dto.getRoomTypeId())
                .roomId(dto.getRoomId())
                .reserveDate(dto.getReserveDate())
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime())
                .seeState(dto.getSeeState())
                .build();
    }


    /**
     * 将创建请求DTO转换为领域模型
     */
    public ReserveSeeHouse toDomainFromCreateRequest(ReserveSeeHouseCreateRequestDTO dto) {
        return ReserveSeeHouse.builder()
                .customerId(dto.getCustomerId())
                .reserveSeeHouseId(dto.getReserveSeeHouseId()) // 添加这行
                .staffId(dto.getStaffId())
                .storeId(dto.getStoreId())
                .roomTypeId(dto.getRoomTypeId())
                .roomId(dto.getRoomId())
                .reserveDate(dto.getReserveDate())
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime())
                .seeState(dto.getSeeState())
                .build();
    }

    /**
     * 将领域模型转换为创建响应DTO
     */
    public ReserveSeeHouseCreateResponseDTO toCreateResponseDTO(ReserveSeeHouse reserveSeeHouse) {
        return ReserveSeeHouseCreateResponseDTO.builder()
                .id(reserveSeeHouse.getId())
                .reserveSeeHouseId(reserveSeeHouse.getReserveSeeHouseId())
                .customerId(reserveSeeHouse.getCustomerId())
                .staffId(reserveSeeHouse.getStaffId())
                .storeId(reserveSeeHouse.getStoreId())
                .roomTypeId(reserveSeeHouse.getRoomTypeId())
                .roomId(reserveSeeHouse.getRoomId())
                .reserveDate(reserveSeeHouse.getReserveDate())
                .startTime(reserveSeeHouse.getStartTime())
                .endTime(reserveSeeHouse.getEndTime())
                .seeState(reserveSeeHouse.getSeeState())
                .build();
    }
}

package com.inboyu.sales.app.dto.common.store;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 当天预约开放状态DTO
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
@Data
@Builder
@Schema(description = "当天预约开放状态")
public class SeeHouseTodayEnabledDTO {
    
    @Schema(description = "状态编码")
    private String code;
    
    @Schema(description = "状态值")
    private String value;
    
    // 常量定义
    public static final String STATUS_ENABLED = "status.enabled";
    public static final String STATUS_DISABLED = "status.disabled";
}

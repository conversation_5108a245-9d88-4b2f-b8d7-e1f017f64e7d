package com.inboyu.sales.app.dto.common.store;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 预约有效天数DTO
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
@Data
@Builder
@Schema(description = "预约有效天数")
public class SeeHouseValidDayDTO {
    
    @Schema(description = "天数编码")
    private String code;
    
    @Schema(description = "天数值")
    private String value;
    
    // 常量定义
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7 = "store_sales_config_see_house_valid_day.7";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_14 = "store_sales_config_see_house_valid_day.14";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_30 = "store_sales_config_see_house_valid_day.30";
}

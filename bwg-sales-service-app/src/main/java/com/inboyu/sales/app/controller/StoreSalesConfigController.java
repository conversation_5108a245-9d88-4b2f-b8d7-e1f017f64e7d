package com.inboyu.sales.app.controller;

import com.inboyu.sales.app.application.StoreSalesConfigAppService;
import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/store-sales-config")
@Tag(name = "门店销售配置控制器")
public class StoreSalesConfigController {

    @Autowired
    private StoreSalesConfigAppService storeSalesConfigAppService;

    @PostMapping
    @Operation(summary = "创建门店销售配置")
    public ResponseEntity<StoreSalesConfigDTO> createStoreSalesConfig(
            @Valid @RequestBody CreateStoreSalesConfigRequestDTO request) {
        log.info("创建门店销售配置请求，门店ID: {}", request.getStoreId());
        
        StoreSalesConfigDTO result = storeSalesConfigAppService.createStoreSalesConfig(request);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/{storeId}")
    @Operation(summary = "根据门店ID查询销售配置")
    public ResponseEntity<StoreSalesConfigDTO> getStoreSalesConfigByStoreId(
            @PathVariable @Parameter(description = "门店ID") Long storeId) {
        log.info("根据门店ID查询销售配置，门店ID: {}", storeId);
        
        StoreSalesConfigDTO result = storeSalesConfigAppService.getStoreSalesConfigByStoreId(storeId);
        if (result == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(result);
    }

    @PostMapping("/update")
    @Operation(summary = "更新门店销售配置")
    public ResponseEntity<StoreSalesConfigDTO> updateStoreSalesConfig(
            @Valid @RequestBody UpdateStoreSalesConfigRequestDTO request) {
        log.info("更新门店销售配置，ID: {}", request.getId());

        StoreSalesConfigDTO result = storeSalesConfigAppService.updateStoreSalesConfig(request);
        return ResponseEntity.ok(result);
    }
}

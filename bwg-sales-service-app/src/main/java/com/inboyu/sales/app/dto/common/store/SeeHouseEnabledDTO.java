package com.inboyu.sales.app.dto.common.store;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

/**
 * 预约看房开放状态DTO
 *
 * <AUTHOR>
 * @date 2025/9/4
 */
@Data
@Builder
@Schema(description = "预约看房开放状态")
public class SeeHouseEnabledDTO {

    /**
     * 状态编码
     */
    @Schema(description = "状态编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "状态编码不能为空")
    private String code;
    /**
     * 角色状态描述
     */
    @Schema(description = "状态描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "状态描述不能为空")
    private String title;
}

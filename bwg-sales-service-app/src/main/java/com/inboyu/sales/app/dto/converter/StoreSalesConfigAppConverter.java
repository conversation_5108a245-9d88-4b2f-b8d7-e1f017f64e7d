package com.inboyu.sales.app.dto.converter;

import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import com.inboyu.sales.domain.store.model.*;
import org.springframework.stereotype.Component;

@Component
public class StoreSalesConfigAppConverter {

    /**
     * 将领域模型转换为DTO
     */
    public StoreSalesConfigDTO toStoreSalesConfigDTO(StoreSalesConfig storeSalesConfig) {
        if (storeSalesConfig == null) {
            return null;
        }
        
        return StoreSalesConfigDTO.builder()
                .id(storeSalesConfig.getId())
                .createTime(storeSalesConfig.getCreateTime())
                .modifyTime(storeSalesConfig.getModifyTime())
                .version(storeSalesConfig.getVersion())
                .deleted(storeSalesConfig.getDeleted())
                .storeId(storeSalesConfig.getStoreId().getValue())
                .seeHouseEnabled(storeSalesConfig.getSeeHouseEnabled().getCode())
                .seeHouseDate(storeSalesConfig.getSeeHouseDate())
                .seeHouseWeekDay(storeSalesConfig.getSeeHouseWeekDay().getCode())
                .seeHouseTimeStart(storeSalesConfig.getSeeHouseTimeStart())
                .seeHouseTimeEnd(storeSalesConfig.getSeeHouseTimeEnd())
                .timeEnabled(storeSalesConfig.getTimeEnabled().getCode())
                .seeHouseTodayEnabled(storeSalesConfig.getSeeHouseTodayEnabled().getCode())
                .seeHouseValidDay(storeSalesConfig.getSeeHouseValidDay().getCode())
                .build();
    }

    /**
     * 将创建请求转换为领域模型
     */
    public StoreSalesConfig toStoreSalesConfig(CreateStoreSalesConfigRequestDTO request) {
        if (request == null) {
            return null;
        }

        return StoreSalesConfig.builder()
                .storeId(StoreId.valueOf(request.getStoreId()))
                .seeHouseEnabled(SeeHouseEnabled.builder()
                        .code(request.getSeeHouseEnabled() ? SeeHouseEnabled.STATUS_ENABLED : SeeHouseEnabled.STATUS_DISABLED)
                        .value(request.getSeeHouseEnabled().toString())
                        .build())
                .seeHouseDate(request.getSeeHouseDate())
                .seeHouseWeekDay(SeeHouseWeekDay.builder()
                        .code(request.getSeeHouseWeekDay())
                        .value(request.getSeeHouseWeekDay())
                        .build())
                .seeHouseTimeStart(request.getSeeHouseTimeStart())
                .seeHouseTimeEnd(request.getSeeHouseTimeEnd())
                .timeEnabled(TimeEnabled.builder()
                        .code(request.getTimeEnabled() ? TimeEnabled.STATUS_ENABLED : TimeEnabled.STATUS_DISABLED)
                        .value(request.getTimeEnabled().toString())
                        .build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder()
                        .code(request.getSeeHouseTodayEnabled() ? SeeHouseTodayEnabled.STATUS_ENABLED : SeeHouseTodayEnabled.STATUS_DISABLED)
                        .value(request.getSeeHouseTodayEnabled().toString())
                        .build())
                .seeHouseValidDay(request.getSeeHouseValidDay())
                .build();
    }

    /**
     * 将更新请求转换为领域模型
     */
    public StoreSalesConfig toStoreSalesConfig(UpdateStoreSalesConfigRequestDTO request) {
        if (request == null) {
            return null;
        }

        return StoreSalesConfig.builder()
                .id(request.getId())
                .storeId(StoreId.valueOf(request.getStoreId()))
                .seeHouseEnabled(SeeHouseEnabled.builder()
                        .code(request.getSeeHouseEnabled() ? SeeHouseEnabled.STATUS_ENABLED : SeeHouseEnabled.STATUS_DISABLED)
                        .value(request.getSeeHouseEnabled().toString())
                        .build())
                .seeHouseDate(request.getSeeHouseDate())
                .seeHouseWeekDay(SeeHouseWeekDay.builder()
                        .code(request.getSeeHouseWeekDay())
                        .value(request.getSeeHouseWeekDay())
                        .build())
                .seeHouseTimeStart(request.getSeeHouseTimeStart())
                .seeHouseTimeEnd(request.getSeeHouseTimeEnd())
                .timeEnabled(TimeEnabled.builder()
                        .code(request.getTimeEnabled() ? TimeEnabled.STATUS_ENABLED : TimeEnabled.STATUS_DISABLED)
                        .value(request.getTimeEnabled().toString())
                        .build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder()
                        .code(request.getSeeHouseTodayEnabled() ? SeeHouseTodayEnabled.STATUS_ENABLED : SeeHouseTodayEnabled.STATUS_DISABLED)
                        .value(request.getSeeHouseTodayEnabled().toString())
                        .build())
                .seeHouseValidDay(request.getSeeHouseValidDay())
                .version(request.getVersion())
                .build();
    }
}

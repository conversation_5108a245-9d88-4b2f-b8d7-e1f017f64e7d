package com.inboyu.sales.app.dto.converter;

import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import com.inboyu.sales.domain.store.model.*;
import org.springframework.stereotype.Component;

@Component
public class StoreSalesConfigAppConverter {

    /**
     * 将领域模型转换为DTO
     */
    public StoreSalesConfigDTO toStoreSalesConfigDTO(StoreSalesConfig storeSalesConfig) {
        if (storeSalesConfig == null) {
            return null;
        }
        
        return StoreSalesConfigDTO.builder()
                .id(storeSalesConfig.getId())
                .createTime(storeSalesConfig.getCreateTime())
                .modifyTime(storeSalesConfig.getModifyTime())
                .version(storeSalesConfig.getVersion())
                .deleted(storeSalesConfig.getDeleted())
                .storeId(storeSalesConfig.getStoreId() != null ? storeSalesConfig.getStoreId().getValue() : null)
                .seeHouseEnabled(storeSalesConfig.getSeeHouseEnabled() != null ? storeSalesConfig.getSeeHouseEnabled().getCode() : null)
                .seeHouseDate(storeSalesConfig.getSeeHouseDate())
                .seeHouseWeekDay(storeSalesConfig.getSeeHouseWeekDay() != null ? storeSalesConfig.getSeeHouseWeekDay().getCode() : null)
                .seeHouseTimeStart(storeSalesConfig.getSeeHouseTimeStart())
                .seeHouseTimeEnd(storeSalesConfig.getSeeHouseTimeEnd())
                .timeEnabled(storeSalesConfig.getTimeEnabled() != null ? storeSalesConfig.getTimeEnabled().getCode() : null)
                .seeHouseTodayEnabled(storeSalesConfig.getSeeHouseTodayEnabled() != null ? storeSalesConfig.getSeeHouseTodayEnabled().getCode() : null)
                .seeHouseValidDay(storeSalesConfig.getSeeHouseValidDay() != null ? storeSalesConfig.getSeeHouseValidDay().getCode() : null)
                .build();
    }

    /**
     * 将创建请求转换为领域模型
     */
    public StoreSalesConfig toStoreSalesConfig(CreateStoreSalesConfigRequestDTO request) {
        if (request == null) {
            return null;
        }

        return StoreSalesConfig.builder()
                .storeId(StoreId.valueOf(request.getStoreId()))
                .seeHouseEnabled(SeeHouseEnabled.builder()
                        .code(request.getSeeHouseEnabled() ? SeeHouseEnabled.STATUS_ENABLED : SeeHouseEnabled.STATUS_DISABLED)
                        .value(request.getSeeHouseEnabled().toString())
                        .build())
                .seeHouseDate(request.getSeeHouseDate())
                .seeHouseWeekDay(SeeHouseWeekDay.builder()
                        .code(request.getSeeHouseWeekDay())
                        .value(request.getSeeHouseWeekDay())
                        .build())
                .seeHouseTimeStart(request.getSeeHouseTimeStart())
                .seeHouseTimeEnd(request.getSeeHouseTimeEnd())
                .timeEnabled(TimeEnabled.builder()
                        .code(request.getTimeEnabled() ? TimeEnabled.STATUS_ENABLED : TimeEnabled.STATUS_DISABLED)
                        .value(request.getTimeEnabled().toString())
                        .build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder()
                        .code(request.getSeeHouseTodayEnabled() ? SeeHouseTodayEnabled.STATUS_ENABLED : SeeHouseTodayEnabled.STATUS_DISABLED)
                        .value(request.getSeeHouseTodayEnabled().toString())
                        .build())
                .seeHouseValidDay(SeeHouseValidDay.builder()
                        .code(mapValidDayToCode(request.getSeeHouseValidDay()))
                        .value(request.getSeeHouseValidDay().toString())
                        .build())
                .build();
    }

    /**
     * 将更新请求转换为领域模型
     */
    public StoreSalesConfig toStoreSalesConfig(UpdateStoreSalesConfigRequestDTO request) {
        if (request == null) {
            return null;
        }

        return StoreSalesConfig.builder()
                .id(request.getId())
                .storeId(StoreId.valueOf(request.getStoreId()))
                .seeHouseEnabled(SeeHouseEnabled.builder()
                        .code(request.getSeeHouseEnabled() ? SeeHouseEnabled.STATUS_ENABLED : SeeHouseEnabled.STATUS_DISABLED)
                        .value(request.getSeeHouseEnabled().toString())
                        .build())
                .seeHouseDate(request.getSeeHouseDate())
                .seeHouseWeekDay(SeeHouseWeekDay.builder()
                        .code(request.getSeeHouseWeekDay())
                        .value(request.getSeeHouseWeekDay())
                        .build())
                .seeHouseTimeStart(request.getSeeHouseTimeStart())
                .seeHouseTimeEnd(request.getSeeHouseTimeEnd())
                .timeEnabled(TimeEnabled.builder()
                        .code(request.getTimeEnabled() ? TimeEnabled.STATUS_ENABLED : TimeEnabled.STATUS_DISABLED)
                        .value(request.getTimeEnabled().toString())
                        .build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder()
                        .code(request.getSeeHouseTodayEnabled() ? SeeHouseTodayEnabled.STATUS_ENABLED : SeeHouseTodayEnabled.STATUS_DISABLED)
                        .value(request.getSeeHouseTodayEnabled().toString())
                        .build())
                .seeHouseValidDay(SeeHouseValidDay.builder()
                        .code(mapValidDayToCode(request.getSeeHouseValidDay()))
                        .value(request.getSeeHouseValidDay().toString())
                        .build())
                .version(request.getVersion())
                .build();
    }

    /**
     * 将有效天数映射为对应的编码
     */
    private String mapValidDayToCode(Integer validDay) {
        if (validDay == null) {
            return null;
        }

        switch (validDay) {
            case 7:
                return SeeHouseValidDay.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7;
            case 14:
                return SeeHouseValidDay.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_14;
            case 30:
                return SeeHouseValidDay.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_30;
            default:
                // 对于其他值，生成动态编码
                return "store_sales_config_see_house_valid_day." + validDay;
        }
    }
}

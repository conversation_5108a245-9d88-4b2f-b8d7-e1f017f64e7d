package com.inboyu.sales.app.dto.converter;

import com.inboyu.sales.app.dto.common.store.*;
import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import com.inboyu.sales.domain.store.model.*;
import org.springframework.stereotype.Component;

@Component
public class StoreSalesConfigAppConverter {

    /**
     * 将领域模型转换为DTO
     */
    public StoreSalesConfigDTO toStoreSalesConfigDTO(StoreSalesConfig storeSalesConfig) {
        if (storeSalesConfig == null) {
            return null;
        }
        
        return StoreSalesConfigDTO.builder()
                .id(String.valueOf(storeSalesConfig.getId()))
                .storeId(String.valueOf((storeSalesConfig.getStoreId() != null ? storeSalesConfig.getStoreId().getValue() : null)))
                .seeHouseEnabled(convertToSeeHouseEnabledDTO(storeSalesConfig.getSeeHouseEnabled()))
                .seeHouseDate(storeSalesConfig.getSeeHouseDate())
                .seeHouseWeekDay(convertToSeeHouseWeekDayDTO(storeSalesConfig.getSeeHouseWeekDay()))
                .seeHouseTimeStart(storeSalesConfig.getSeeHouseTimeStart())
                .seeHouseTimeEnd(storeSalesConfig.getSeeHouseTimeEnd())
                .timeEnabled(convertToTimeEnabledDTO(storeSalesConfig.getTimeEnabled()))
                .seeHouseTodayEnabled(convertToSeeHouseTodayEnabledDTO(storeSalesConfig.getSeeHouseTodayEnabled()))
                .seeHouseValidDay(convertToSeeHouseValidDayDTO(storeSalesConfig.getSeeHouseValidDay()))
                .build();
    }

    /**
     * 将创建请求转换为领域模型
     */
    public StoreSalesConfig toStoreSalesConfig(CreateStoreSalesConfigRequestDTO request) {
        if (request == null) {
            return null;
        }

        return StoreSalesConfig.builder()
                .storeId(StoreId.of(Long.valueOf(request.getStoreId())))
                .seeHouseEnabled(convertFromSeeHouseEnabledDTO(request.getSeeHouseEnabled()))
                .seeHouseDate(request.getSeeHouseDate())
                .seeHouseWeekDay(convertFromSeeHouseWeekDayDTO(request.getSeeHouseWeekDay()))
                .seeHouseTimeStart(request.getSeeHouseTimeStart())
                .seeHouseTimeEnd(request.getSeeHouseTimeEnd())
                .timeEnabled(convertFromTimeEnabledDTO(request.getTimeEnabled()))
                .seeHouseTodayEnabled(convertFromSeeHouseTodayEnabledDTO(request.getSeeHouseTodayEnabled()))
                .seeHouseValidDay(convertFromSeeHouseValidDayDTO(request.getSeeHouseValidDay()))
                .build();
    }

    /**
     * 将更新请求转换为领域模型
     */
    public StoreSalesConfig toStoreSalesConfig(UpdateStoreSalesConfigRequestDTO request) {
        if (request == null) {
            return null;
        }

        return StoreSalesConfig.builder()
                .id(Long.valueOf(request.getId()))
                .storeId(StoreId.of(Long.valueOf(request.getStoreId())))
                .seeHouseEnabled(convertFromSeeHouseEnabledDTO(request.getSeeHouseEnabled()))
                .seeHouseDate(request.getSeeHouseDate())
                .seeHouseWeekDay(convertFromSeeHouseWeekDayDTO(request.getSeeHouseWeekDay()))
                .seeHouseTimeStart(request.getSeeHouseTimeStart())
                .seeHouseTimeEnd(request.getSeeHouseTimeEnd())
                .timeEnabled(convertFromTimeEnabledDTO(request.getTimeEnabled()))
                .seeHouseTodayEnabled(convertFromSeeHouseTodayEnabledDTO(request.getSeeHouseTodayEnabled()))
                .seeHouseValidDay(convertFromSeeHouseValidDayDTO(request.getSeeHouseValidDay()))
                .version(request.getVersion())
                .build();
    }

    // ========== 领域对象转DTO方法 ==========

    private SeeHouseEnabledDTO convertToSeeHouseEnabledDTO(SeeHouseEnabled seeHouseEnabled) {
        if (seeHouseEnabled == null) {
            return null;
        }
        return SeeHouseEnabledDTO.builder()
                .code(seeHouseEnabled.getCode())
                .build();
    }

    private SeeHouseWeekDayDTO convertToSeeHouseWeekDayDTO(SeeHouseWeekDay seeHouseWeekDay) {
        if (seeHouseWeekDay == null) {
            return null;
        }
        return SeeHouseWeekDayDTO.builder()
                .code(seeHouseWeekDay.getCode())
                .build();
    }

    private TimeEnabledDTO convertToTimeEnabledDTO(TimeEnabled timeEnabled) {
        if (timeEnabled == null) {
            return null;
        }
        return TimeEnabledDTO.builder()
                .code(timeEnabled.getCode())
                .build();
    }

    private SeeHouseTodayEnabledDTO convertToSeeHouseTodayEnabledDTO(SeeHouseTodayEnabled seeHouseTodayEnabled) {
        if (seeHouseTodayEnabled == null) {
            return null;
        }
        return SeeHouseTodayEnabledDTO.builder()
                .code(seeHouseTodayEnabled.getCode())
                .build();
    }

    private SeeHouseValidDayDTO convertToSeeHouseValidDayDTO(SeeHouseValidDay seeHouseValidDay) {
        if (seeHouseValidDay == null) {
            return null;
        }
        return SeeHouseValidDayDTO.builder()
                .code(seeHouseValidDay.getCode())
                .build();
    }

    // ========== DTO转领域对象方法 ==========

    private SeeHouseEnabled convertFromSeeHouseEnabledDTO(SeeHouseEnabledDTO dto) {
        if (dto == null) {
            return null;
        }
        return SeeHouseEnabled.builder()
                .code(dto.getCode())
                .build();
    }

    private SeeHouseWeekDay convertFromSeeHouseWeekDayDTO(SeeHouseWeekDayDTO dto) {
        if (dto == null) {
            return null;
        }
        return SeeHouseWeekDay.builder()
                .code(dto.getCode())
                .build();
    }

    private TimeEnabled convertFromTimeEnabledDTO(TimeEnabledDTO dto) {
        if (dto == null) {
            return null;
        }
        return TimeEnabled.builder()
                .code(dto.getCode())
                .build();
    }

    private SeeHouseTodayEnabled convertFromSeeHouseTodayEnabledDTO(SeeHouseTodayEnabledDTO dto) {
        if (dto == null) {
            return null;
        }
        return SeeHouseTodayEnabled.builder()
                .code(dto.getCode())
                .build();
    }

    private SeeHouseValidDay convertFromSeeHouseValidDayDTO(SeeHouseValidDayDTO dto) {
        if (dto == null) {
            return null;
        }
        return SeeHouseValidDay.builder()
                .code(dto.getCode())
                .build();
    }
}

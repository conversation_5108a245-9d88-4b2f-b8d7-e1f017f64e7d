package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.application.StoreSalesConfigAppService;
import com.inboyu.sales.app.dto.converter.StoreSalesConfigAppConverter;
import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.domain.store.model.StoreSalesConfig;
import com.inboyu.sales.domain.store.service.StoreSalesConfigDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class StoreSalesConfigAppServiceImpl implements StoreSalesConfigAppService {

    @Autowired
    private StoreSalesConfigDomainService storeSalesConfigDomainService;

    @Autowired
    private StoreSalesConfigAppConverter storeSalesConfigAppConverter;

    @Override
    public StoreSalesConfigDTO createStoreSalesConfig(CreateStoreSalesConfigRequestDTO request) {
        log.info("创建门店销售配置，门店ID: {}", request.getStoreId());
        
        StoreSalesConfig storeSalesConfig = storeSalesConfigAppConverter.toStoreSalesConfig(request);
        StoreSalesConfig createdConfig = storeSalesConfigDomainService.createStoreSalesConfig(storeSalesConfig);
        
        return storeSalesConfigAppConverter.toStoreSalesConfigDTO(createdConfig);
    }

    @Override
    public StoreSalesConfigDTO getStoreSalesConfigByStoreId(Long storeId) {
        log.info("根据门店ID查询销售配置，门店ID: {}", storeId);

        StoreSalesConfig storeSalesConfig = storeSalesConfigDomainService.getStoreSalesConfigByStoreId(StoreId.valueOf(storeId));
        if (null == storeSalesConfig) {
            log.error("查询门店小时配置失败,原因:门店不存在,门店id:{}", storeId);
        }
        return storeSalesConfigAppConverter.toStoreSalesConfigDTO(storeSalesConfig);
    }

    @Override
    public StoreSalesConfigDTO updateStoreSalesConfig(UpdateStoreSalesConfigRequestDTO request) {
        log.info("更新门店销售配置，ID: {}", request.getId());

        StoreSalesConfig storeSalesConfig = storeSalesConfigAppConverter.toStoreSalesConfig(request);
        StoreSalesConfig updatedConfig = storeSalesConfigDomainService.updateStoreSalesConfig(storeSalesConfig);

        return storeSalesConfigAppConverter.toStoreSalesConfigDTO(updatedConfig);
    }
}

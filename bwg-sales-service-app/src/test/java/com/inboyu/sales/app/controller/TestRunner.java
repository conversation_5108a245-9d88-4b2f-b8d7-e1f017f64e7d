package com.inboyu.sales.app.controller;

/**
 * 简单的测试运行器，用于验证测试是否能正常工作
 */
public class TestRunner {
    
    public static void main(String[] args) {
        System.out.println("开始运行StoreSalesConfigControllerTest...");
        
        try {
            StoreSalesConfigControllerTest test = new StoreSalesConfigControllerTest();
            test.setUp();
            
            // 运行所有测试方法
            System.out.println("运行 testCreateStoreSalesConfig_Success...");
            test.testCreateStoreSalesConfig_Success();
            System.out.println("✓ testCreateStoreSalesConfig_Success 通过");
            
            System.out.println("运行 testCreateStoreSalesConfig_NullRequest...");
            test.testCreateStoreSalesConfig_NullRequest();
            System.out.println("✓ testCreateStoreSalesConfig_NullRequest 通过");
            
            System.out.println("运行 testGetStoreSalesConfigByStoreId_Success...");
            test.testGetStoreSalesConfigByStoreId_Success();
            System.out.println("✓ testGetStoreSalesConfigByStoreId_Success 通过");
            
            System.out.println("运行 testGetStoreSalesConfigByStoreId_NotFound...");
            test.testGetStoreSalesConfigByStoreId_NotFound();
            System.out.println("✓ testGetStoreSalesConfigByStoreId_NotFound 通过");
            
            System.out.println("运行 testUpdateStoreSalesConfig_Success...");
            test.testUpdateStoreSalesConfig_Success();
            System.out.println("✓ testUpdateStoreSalesConfig_Success 通过");
            
            System.out.println("运行 testUpdateStoreSalesConfig_NullRequest...");
            test.testUpdateStoreSalesConfig_NullRequest();
            System.out.println("✓ testUpdateStoreSalesConfig_NullRequest 通过");
            
            System.out.println("\n所有测试都通过了！ ✓");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}

package com.inboyu.sales.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inboyu.sales.app.application.StoreSalesConfigAppService;
import com.inboyu.sales.app.dto.common.store.*;
import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * StoreSalesConfigController 单元测试
 */
@WebMvcTest(StoreSalesConfigController.class)
class StoreSalesConfigControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StoreSalesConfigAppService storeSalesConfigAppService;

    @Autowired
    private ObjectMapper objectMapper;

    private CreateStoreSalesConfigRequestDTO createRequest;
    private UpdateStoreSalesConfigRequestDTO updateRequest;
    private StoreSalesConfigDTO responseDTO;

    @BeforeEach
    void setUp() {
        // 准备创建请求DTO
        createRequest = new CreateStoreSalesConfigRequestDTO();
        createRequest.setStoreId(100L);
        createRequest.setSeeHouseEnabled(SeeHouseEnabledDTO.builder()
                .code(SeeHouseEnabledDTO.STATUS_ENABLED)
                .value("true")
                .build());
        createRequest.setSeeHouseDate(LocalDate.of(2025, 9, 4));
        createRequest.setSeeHouseWeekDay(SeeHouseWeekDayDTO.builder()
                .code(SeeHouseWeekDayDTO.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_MONDAY)
                .value("Monday")
                .build());
        createRequest.setSeeHouseTimeStart(LocalTime.of(9, 0));
        createRequest.setSeeHouseTimeEnd(LocalTime.of(18, 0));
        createRequest.setTimeEnabled(TimeEnabledDTO.builder()
                .code(TimeEnabledDTO.STATUS_ENABLED)
                .value("true")
                .build());
        createRequest.setSeeHouseTodayEnabled(SeeHouseTodayEnabledDTO.builder()
                .code(SeeHouseTodayEnabledDTO.STATUS_DISABLED)
                .value("false")
                .build());
        createRequest.setSeeHouseValidDay(SeeHouseValidDayDTO.builder()
                .code(SeeHouseValidDayDTO.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7)
                .value("7")
                .build());

        // 准备更新请求DTO
        updateRequest = new UpdateStoreSalesConfigRequestDTO();
        updateRequest.setId(1L);
        updateRequest.setStoreId(100L);
        updateRequest.setSeeHouseEnabled(SeeHouseEnabledDTO.builder()
                .code(SeeHouseEnabledDTO.STATUS_DISABLED)
                .value("false")
                .build());
        updateRequest.setSeeHouseDate(LocalDate.of(2025, 9, 5));
        updateRequest.setSeeHouseWeekDay(SeeHouseWeekDayDTO.builder()
                .code(SeeHouseWeekDayDTO.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_TUESDAY)
                .value("Tuesday")
                .build());
        updateRequest.setSeeHouseTimeStart(LocalTime.of(10, 0));
        updateRequest.setSeeHouseTimeEnd(LocalTime.of(17, 0));
        updateRequest.setTimeEnabled(TimeEnabledDTO.builder()
                .code(TimeEnabledDTO.STATUS_DISABLED)
                .value("false")
                .build());
        updateRequest.setSeeHouseTodayEnabled(SeeHouseTodayEnabledDTO.builder()
                .code(SeeHouseTodayEnabledDTO.STATUS_ENABLED)
                .value("true")
                .build());
        updateRequest.setSeeHouseValidDay(SeeHouseValidDayDTO.builder()
                .code(SeeHouseValidDayDTO.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_14)
                .value("14")
                .build());
        updateRequest.setVersion(1L);

        // 准备响应DTO
        responseDTO = StoreSalesConfigDTO.builder()
                .id(1L)
                .storeId(100L)
                .createTime(LocalDateTime.now())
                .modifyTime(LocalDateTime.now())
                .version(1L)
                .deleted(0)
                .seeHouseEnabled(SeeHouseEnabledDTO.builder()
                        .code(SeeHouseEnabledDTO.STATUS_ENABLED)
                        .value("true")
                        .build())
                .seeHouseDate(LocalDate.of(2025, 9, 4))
                .seeHouseWeekDay(SeeHouseWeekDayDTO.builder()
                        .code(SeeHouseWeekDayDTO.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_MONDAY)
                        .value("Monday")
                        .build())
                .seeHouseTimeStart(LocalTime.of(9, 0))
                .seeHouseTimeEnd(LocalTime.of(18, 0))
                .timeEnabled(TimeEnabledDTO.builder()
                        .code(TimeEnabledDTO.STATUS_ENABLED)
                        .value("true")
                        .build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabledDTO.builder()
                        .code(SeeHouseTodayEnabledDTO.STATUS_DISABLED)
                        .value("false")
                        .build())
                .seeHouseValidDay(SeeHouseValidDayDTO.builder()
                        .code(SeeHouseValidDayDTO.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7)
                        .value("7")
                        .build())
                .build();
    }

    @Test
    void testCreateStoreSalesConfig_Success() throws Exception {
        // Given
        when(storeSalesConfigAppService.createStoreSalesConfig(any(CreateStoreSalesConfigRequestDTO.class)))
                .thenReturn(responseDTO);

        // When & Then
        mockMvc.perform(post("/api/v1/store-sales-config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.storeId").value(100L))
                .andExpect(jsonPath("$.seeHouseEnabled.code").value(SeeHouseEnabledDTO.STATUS_ENABLED))
                .andExpect(jsonPath("$.seeHouseWeekDay.code").value(SeeHouseWeekDayDTO.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_MONDAY))
                .andExpect(jsonPath("$.timeEnabled.code").value(TimeEnabledDTO.STATUS_ENABLED))
                .andExpect(jsonPath("$.seeHouseTodayEnabled.code").value(SeeHouseTodayEnabledDTO.STATUS_DISABLED))
                .andExpect(jsonPath("$.seeHouseValidDay.code").value(SeeHouseValidDayDTO.STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7));
    }

    @Test
    void testCreateStoreSalesConfig_ValidationError() throws Exception {
        // Given - 创建一个无效的请求（缺少必填字段）
        CreateStoreSalesConfigRequestDTO invalidRequest = new CreateStoreSalesConfigRequestDTO();
        // 不设置任何字段，触发验证错误

        // When & Then
        mockMvc.perform(post("/api/v1/store-sales-config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetStoreSalesConfigByStoreId_Success() throws Exception {
        // Given
        Long storeId = 100L;
        when(storeSalesConfigAppService.getStoreSalesConfigByStoreId(eq(storeId)))
                .thenReturn(responseDTO);

        // When & Then
        mockMvc.perform(get("/api/v1/store-sales-config/{storeId}", storeId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.storeId").value(100L))
                .andExpect(jsonPath("$.seeHouseEnabled.code").value(SeeHouseEnabledDTO.STATUS_ENABLED));
    }

    @Test
    void testGetStoreSalesConfigByStoreId_NotFound() throws Exception {
        // Given
        Long storeId = 999L;
        when(storeSalesConfigAppService.getStoreSalesConfigByStoreId(eq(storeId)))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(get("/api/v1/store-sales-config/{storeId}", storeId))
                .andExpect(status().isNotFound());
    }
